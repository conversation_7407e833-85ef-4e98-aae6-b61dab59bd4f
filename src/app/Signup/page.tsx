"use client"
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link"
import {axios} from "axios";


export default function Signup() {

  const [user, setuser] = useState({
    email: "",
    password: "",
    username: "",
  })

  const onSignUp = async () => {
    try {
      await axios.post("/api/users/signup", user)
      router.push("/login")
    } catch (error: any) {
      console.log(error.message)
    }
  }
  return (
    <div>
      <h1 className="text-3xl font text-center mt-20">Sign up</h1>
    </div>
  );
}
