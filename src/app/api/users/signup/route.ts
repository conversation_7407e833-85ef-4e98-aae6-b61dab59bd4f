import {connect} from '@/dbConfig/dbConfig';
import User from '@/models/userModel';
import { NextResponse, NextRequest } from 'next/server';
import bcryptjs from 'bcryptjs';


connect();

export async function POST(request: NextRequest) {
    try {
        const reqBody = await request.json()
        const {username,email,password}=reqBody;

        const IsUser = await User.findOne({
            email:email,
        })

        if(IsUser){
            return NextResponse.json({error:"User already exists"},{status:400});
        }
        const salt = await bcryptjs.genSalt(10);
        const hashedPassword = await bcryptjs.hash(password,salt);  

        const newUser = new User({
            username,
            email,
            password:hashedPassword,
        })

        const savedUser = await newUser.save();

        return NextResponse.json({
            message:"User created successfully",
            success:true,
            savedUser,
        })

    } catch (error : any) {
        return NextResponse.json(
            {error:error.message},
            {status:500});
    }
}