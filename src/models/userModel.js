import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
    username:{
        type:String,
        required:true,
        unique:true,
    },
    email:{
        type:String,
        required:true,
        unique:true,
    },
    password:{
        type:String,
        required:true,
    },
    isVerified:{
        type:Boolean,
        default:false,
    },
    verifyToken:{
        type:String,
    },
    verifyTokenExpiry:{
        type:Date,
    },
    resetPasswordToken:{
        type:String,
    },
    resetPasswordTokenExpiry:{
        type:Date,
    },
});

const User = mongoose.models.User || mongoose.model("User", userSchema);

export default User;
